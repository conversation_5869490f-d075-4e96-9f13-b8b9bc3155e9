# SSL证书自动化申请系统设计文档

## 项目概述
本项目实现了SSL证书申请的全流程自动化，通过Web接口接收域名提交，经过Python3.8程序的智能过滤和处理，在CentOS7服务器上完成域名检测、证书申请及宝塔站点配置的自动化操作。

## 技术栈
- 编程语言：Python 3.8+、Shell脚本
- 运行环境：CentOS 7
- Web框架：Flask（域名提交接口）
- 数据存储：Redis 3.2.12
- 通知机制：Telegram机器人（已实现）
- 远程操作：SSH、rsync
- 面板集成：宝塔面板API

## 系统架构

### 数据流转图
```
Web提交接口 → Redis暂存 → 域名过滤检测 → 站点管理 → 证书生成队列 → 多线程处理 → 通知反馈
     ↓                ↓              ↓           ↓             ↓
   用户界面      nginx配置检查    DNS解析验证   宝塔API同步   实时状态跟踪
```

### Redis数据结构设计
| 键名 | 类型 | 用途 | 示例 |
|-----|------|------|------|
| `get_domains` | String | 存储用户提交的域名列表 | "a.com b.com c.com" |
| `domains_cert_sort_set` | ZSet | 待处理域名及失败计数 | {domain: failure_count} |
| `domains_exists_set` | Set | 已存在nginx配置的域名 | {"existing1.com", "existing2.com"} |
| `domains_remove_set` | Set | 失败次数超限的域名 | {"failed1.com", "failed2.com"} |
| `domains_queue_autoa` | List | A组站点证书生成队列 | [{"site_id":"autoa1", "domains":[...]}] |
| `domains_queue_autob` | List | B组站点证书生成队列 | [{"site_id":"autob1", "domains":[...]}] |
| `domains_add_history` | Hash | 站点ID与域名关系记录 | {site_id: site_info_json} |
| `cert_info_{site_id}` | Hash | 证书信息和任务状态 | 包含证书内容、路径、状态等 |
| `cert_retry_{site_id}` | String | 证书生成重试计数器 | "1", "2", "3" |

## 设计思路
系统分为三个主要模块：

### 模块一：域名接收与Web服务
提供Web界面接收用户提交的域名，验证格式后存入Redis，支持批量提交和实时反馈。

### 模块二：域名处理与过滤引擎
从Redis读取域名，执行两轮智能过滤（nginx配置检查、DNS解析验证），管理失败重试机制，按站点组织域名并更新宝塔配置。

### 模块三：多线程证书生成引擎
使用两个独立线程处理A/B组队列，支持失败重试、状态跟踪、实时通知，确保证书生成的可靠性和效率。

## 详细设计

### 模块一：域名接收与Web服务

#### 1. Web提交接口
- **功能描述**：提供用户友好的Web界面，接收域名批量提交请求
- **实现方式**：
  - 使用Flask框架创建Web服务（`domain_submit_server.py`）
  - 提供表单界面，支持空格分隔的域名输入
  - 验证域名格式，去重处理后存入Redis的`get_domains`键
  - 支持追加模式，避免覆盖现有待处理域名
  - 返回JSON格式的提交结果和状态码
- **接口地址**：通常部署在服务器的Web端口
- **数据格式**：接收空格分隔的域名字符串，如"a.com b.com c.com"

#### 2. 数据暂存机制
- **功能描述**：将用户提交的域名安全地暂存到Redis，等待处理器读取
- **实现方式**：
  - 使用Redis的`get_domains`键存储待处理域名
  - 支持多次提交的域名合并，避免重复处理
  - 处理器读取后自动清除，防止重复消费

### 模块二：域名处理与过滤引擎（任务一）

#### 1. 域名获取与读取
- **功能描述**：从Redis读取用户提交的域名列表，启动处理流程
- **实现方式**：
  - 定时（60秒间隔）从Redis的`get_domains`键读取域名
  - 读取后立即删除该键，避免重复处理
  - 支持空轮询检查，即使无新域名也检查已有排序集合中的域名状态
  - 记录详细日志，便于调试和监控

#### 2. 两轮域名过滤机制
- **功能描述**：对域名进行严格的两轮过滤，确保只处理有效且需要证书的域名
- **实现方式**：
  
  **第一轮：nginx配置过滤**
  - 同步宝塔服务器nginx配置到本地：`rsync -az {SSH_BT_HOST}:/www/server/panel/vhost/nginx /tmp/`
  - 使用grep检查域名是否已存在nginx配置：`grep -roP "(^|\\s){domain}(\\s|;|$)" /tmp/nginx/`
  - 已存在域名加入`domains_exists_set`集合，避免重复处理
  - 新域名加入`domains_cert_sort_set`排序集合，初始失败计数为0
  - 对已存在域名发送Telegram通知："{domain} 已存在，请确认：https://{domain}/123.txt"
  
  **第二轮：DNS解析过滤**
  - 读取排序集合中的域名，写入临时文件
  - 调用外部脚本`auto_domains_check.sh`进行批量DNS解析检测
  - 解析脚本返回格式：`SUCCESS:domain1,domain2|FAIL:domain3,domain4`
  - 成功域名从排序集合移除，用于后续站点分配
  - 失败域名计数+1，达到60次上限时移入`domains_remove_set`
  - 发送详细的成功/失败统计通知到Telegram

#### 3. 智能站点管理与队列分配
- **功能描述**：将过滤后的域名智能分配到A/B组站点，管理站点容量，更新宝塔配置
- **实现方式**：
  
  **站点组轮询机制**：
  - 使用`current_group`在a/b组间轮换，避免单组过载
  - 站点命名规则：autoa1, autoa2, autob1, autob2...
  - 每次处理成功后切换组别：a → b → a
  
  **站点容量管理**：
  - 每个站点最大100个域名（`MAX_DOMAINS_PER_SITE = 100`）
  - 通过`auto_get_bt_domains.sh`脚本获取当前站点域名数量
  - 容量不足时自动创建新站点：站点编号递增
  
  **宝塔API集成**：
  - 创建新站点：`python3.8 auto_bt_api.py 'bt.add_site({"webname": "{site_id}"})'`
  - 更新站点域名：`python3.8 auto_bt_api.py 'bt.web_add_domain("{site_id}","{domains_str}")'`
  - 所有站点操作完成后才提交到证书生成队列
  
  **队列路由策略**：
  - autoa*站点 → `domains_queue_autoa`队列
  - autob*站点 → `domains_queue_autob`队列
  - 队列数据格式：`{"site_id": "autoa1", "domains": ["domain1", "domain2"]}`

#### 4. 循环处理与状态管理
- **功能描述**：持续监控和处理域名，维护系统运行状态
- **实现方式**：
  - 主循环每60秒执行一次完整流程
  - 队列状态诊断，记录各队列长度和处理情况
  - 站点组自动切换，确保负载均衡
  - 异常处理和自动恢复，记录详细错误日志

### 模块三：多线程证书生成引擎（任务二）

#### 1. 队列监听与任务分发
- **功能描述**：使用两个独立线程处理A/B组队列，实现并行证书生成
- **实现方式**：
  - 启动两个`QueueWorker`线程，分别监听`domains_queue_autoa`和`domains_queue_autob`
  - 使用`BLPOP`命令阻塞读取队列，超时时间30秒
  - 解析队列中的JSON任务数据，提取站点ID和域名列表
  - 每个线程独立处理，支持并发但避免竞争条件

#### 2. 证书生成任务处理
- **功能描述**：调用证书生成脚本，实时监控处理过程，管理任务状态
- **实现方式**：
  
  **任务执行流程**：
  - 更新任务状态为"processing"：`cert_info_{site_id}`
  - 发送开始处理通知到Telegram
  - 调用证书脚本：`/root/ajie/yunwei/auto_apply_cert.sh {site_id}`
  - 使用多线程实时读取脚本输出，记录到日志
  - 根据脚本返回码判断成功/失败
  
  **成功处理**：
  - 更新状态为"success"，记录处理时间
  - 读取证书文件：`/root/.acme.sh/{site_id}/cert.pem`和`privkey.pem`
  - 存储证书内容到Redis：`cert_info_{site_id}`
  - 发送成功通知：站点ID、耗时、域名数量
  - 清除重试计数器，继续下一任务
  
  **失败处理**：
  - 更新状态为"failed"，记录错误信息
  - 检查队列中是否有相同编号站点任务
  - 有相同编号：跳过重试，直接处理下一任务
  - 无相同编号：进入重试机制

#### 3. 智能重试机制
- **功能描述**：实现可靠的失败重试策略，避免因临时问题导致的证书生成失败
- **实现方式**：
  
  **重试决策逻辑**：
  - 提取站点编号（如autoa3中的"3"）
  - 检查队列中是否存在相同编号的其他任务
  - 存在相同编号：直接跳过，处理下一任务（避免重复工作）
  - 不存在相同编号：执行重试流程
  
  **重试执行**：
  - 最大重试次数：3次（`MAX_RETRIES = 3`）
  - 重试间隔：120秒（`RETRY_INTERVAL = 120`）
  - 重试计数器：`cert_retry_{site_id}`，过期时间1小时
  - 超过重试次数：放弃任务，发送人工干预通知
  - 任务重新加入队列左侧，确保优先处理

#### 4. 状态跟踪与通知系统
- **功能描述**：提供完整的任务状态跟踪和实时通知反馈
- **实现方式**：
  
  **状态管理**：
  - 任务状态：processing, success, failed
  - 存储位置：`cert_info_{site_id}`哈希结构
  - 包含信息：状态、时间戳、错误信息、证书内容、文件路径
  
  **Telegram通知集成**：
  - 开始处理：站点ID + 域名数量
  - 成功完成：站点ID + 耗时 + 域名数量
  - 失败重试：站点ID + 重试次数 + 等待时间
  - 达到重试上限：站点ID + 需要人工确认
  - 异常错误：详细错误信息（截断至200字符）

## 配置参数说明

### Redis配置
```python
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB = 1
REDIS_PASSWORD = 'BBMFqw8uqw65'
```

### 宝塔服务器配置
```python
SSH_BT_HOST = '**************'  # 宝塔主机IP
NGINX_CONF_PATH = '/tmp/nginx/'  # 本地nginx配置同步路径
```

### 业务参数
```python
MAX_DOMAINS_PER_SITE = 100    # 每个站点最大域名数量
MAX_FAILURE_COUNT = 60        # 域名解析失败最大次数
MAX_RETRIES = 3               # 证书生成最大重试次数
RETRY_INTERVAL = 120          # 重试间隔秒数
BLPOP_TIMEOUT = 30           # 队列阻塞超时秒数
```

### 脚本路径
```python
DOMAINS_CHECK_SCRIPT = '/root/ajie/yunwei/auto_domains_check.sh'
DOMAINS_GET_SCRIPT = '/root/ajie/yunwei/auto_get_bt_domains.sh'
APPLY_CERT_SCRIPT = '/root/ajie/yunwei/auto_apply_cert.sh'
```

### Telegram通知
```python
BOT_TOKEN = '**********************************************'
CHAT_ID = '-1002759112051'
```

## 已实现功能

### 核心功能
- ✅ Web域名提交接口（Flask应用）
- ✅ 两轮域名过滤机制（nginx检查 + DNS解析）
- ✅ 智能站点容量管理和自动扩容
- ✅ A/B组队列并行处理
- ✅ 证书生成任务的完整生命周期管理
- ✅ 失败重试机制和智能跳过策略
- ✅ Redis数据持久化和状态跟踪
- ✅ 宝塔API集成（站点创建、域名更新）

### 监控和通知
- ✅ Telegram机器人实时通知
- ✅ 详细的结构化日志记录
- ✅ 任务状态实时跟踪
- ✅ 队列状态诊断工具
- ✅ 异常处理和自动恢复

### 外部脚本集成
- ✅ `auto_domains_check.sh`：批量域名解析检测
- ✅ `auto_get_bt_domains.sh`：获取宝塔站点信息
- ✅ `auto_apply_cert.sh`：证书申请脚本
- ✅ `auto_bt_api.py`：宝塔API调用封装

## 部署依赖

### Python依赖包
```bash
pip3.8 install redis flask python-telegram-bot requests
```

### 系统依赖
- CentOS 7操作系统
- Python 3.8+环境
- Redis 3.2.12+服务
- SSH免密登录到宝塔服务器
- rsync工具（用于配置文件同步）

### 部署注意事项
根据代码注释，部署新环境时需要特别注意以下脚本的路径和配置：
1. `auto_domains_check.sh` - 域名检测脚本
2. `auto_bt_api.py` - 宝塔API调用脚本
3. `domain_submit_server.py` - 域名提交Web服务
4. `auto_apply_cert.sh` - 证书申请脚本

## 暂未实现功能

### 证书续签模块（任务三）
- ⏸️ 证书到各主机的自动同步部署
- ⏸️ 证书续签检查和自动续签
- ⏸️ 宝塔证书小于10天的自动检测

### 增强功能
- ⏸️ Web管理界面（当前仅有提交界面）
- ⏸️ 证书状态查询API
- ⏸️ 成功后调用success接口上报
- ⏸️ 更复杂的负载均衡策略

## 系统流程图

```mermaid
graph TD
    A[用户Web提交] --> B[Redis暂存]
    B --> C[定时读取域名]
    C --> D[nginx配置检查]
    D --> E{域名是否存在}
    E -->|存在| F[加入exists集合]
    E -->|不存在| G[加入cert排序集合]
    G --> H[DNS解析检查]
    H --> I{解析是否成功}
    I -->|失败| J[失败计数+1]
    J --> K{达到失败上限?}
    K -->|是| L[移入remove集合]
    K -->|否| G
    I -->|成功| M[站点容量检查]
    M --> N{站点是否已满}
    N -->|已满| O[创建新站点]
    N -->|未满| P[添加到当前站点]
    O --> P
    P --> Q[更新宝塔配置]
    Q --> R[加入证书队列]
    R --> S[多线程处理]
    S --> T[调用证书脚本]
    T --> U{证书生成成功?}
    U -->|成功| V[保存证书信息]
    U -->|失败| W[检查重试条件]
    W --> X{需要重试?}
    X -->|是| Y[等待后重试]
    X -->|否| Z[放弃任务]
    V --> AA[发送成功通知]
    F --> AB[发送存在通知]
    L --> AC[发送失败通知]
    Z --> AD[发送放弃通知]
```

## 监控指标

### 关键性能指标
- 域名处理速度：每分钟处理的域名数量
- 证书生成成功率：成功/总数的百分比
- 队列积压情况：待处理任务数量
- 系统响应时间：从提交到完成的平均时间

### 异常监控
- Redis连接状态
- 宝塔服务器连接状态
- 脚本执行失败率
- Telegram通知发送状态