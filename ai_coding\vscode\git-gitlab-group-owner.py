import requests
import csv
from datetime import datetime
from urllib.parse import urljoin

# GitLab配置
#GITLAB_URL = "https://gitlab.example.com"  # 替换为你的GitLab地址
#PRIVATE_TOKEN = "your_private_token"      # 需要api权限
#PER_PAGE = 100                           # 每页项目数量
GITLAB_URL = 'http://10.124.131.10:8080'
PRIVATE_TOKEN  = 'g7tB7H1exSVc2p8Wifb2'
PER_PAGE = 100


def get_all_groups():
    """获取所有组列表"""
    groups = []
    page = 1
    while True:
        url = urljoin(GITLAB_URL, f"/api/v4/groups?per_page={PER_PAGE}&page={page}")
        headers = {"PRIVATE-TOKEN": PRIVATE_TOKEN}
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            current_page_groups = response.json()
            if not current_page_groups:
                break
                
            groups.extend(current_page_groups)
            
            # 检查是否有下一页
            if 'next' not in response.links:
                break
                
            page += 1
            
        except requests.exceptions.RequestException as e:
            print(f"获取组列表出错: {str(e)}")
            break
            
    return groups

def get_group_members(group_id):
    """获取组的所有成员信息"""
    members = []
    page = 1
    headers = {"PRIVATE-TOKEN": PRIVATE_TOKEN}

    # 权限级别映射
    access_levels = {
        10: "Guest",
        20: "Reporter",
        30: "Developer",
        40: "Maintainer",
        50: "Owner"
    }

    while True:
        try:
            # 获取组成员，包括继承的成员
            members_url = urljoin(GITLAB_URL, f"/api/v4/groups/{group_id}/members/all?per_page={PER_PAGE}&page={page}")
            response = requests.get(members_url, headers=headers, timeout=10)

            if response.status_code == 200:
                current_page_members = response.json()
                if not current_page_members:
                    break

                for member in current_page_members:
                    member_info = {
                        'name': member.get('name', ''),
                        'username': member.get('username', ''),
                        'email': member.get('email', ''),
                        'access_level': access_levels.get(member.get('access_level', 0), 'Unknown'),
                        'access_level_value': member.get('access_level', 0),
                        'state': member.get('state', ''),
                        'created_at': member.get('created_at', ''),
                        'expires_at': member.get('expires_at', '')
                    }
                    members.append(member_info)

                # 检查是否有下一页
                if 'next' not in response.links:
                    break
                page += 1
            else:
                print(f"获取组{group_id}成员信息失败，状态码: {response.status_code}")
                break

        except requests.exceptions.RequestException as e:
            print(f"获取组{group_id}成员信息时出错: {str(e)}")
            break

    return members

def main():
    print("开始获取GitLab组信息(所有成员统计)...")

    # 获取所有组
    groups = get_all_groups()
    if not groups:
        print("没有获取到任何组")
        return

    # 准备CSV文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"gitlab_groups_members_{timestamp}.csv"

    with open(csv_filename, mode='w', newline='', encoding='utf-8') as csv_file:
        fieldnames = [
            '组ID',
            '组名称',
            '组路径',
            '组描述',
            '组创建时间',
            '组项目数',
            '组子组数',
            '组可见性',
            '成员姓名',
            '成员用户名',
            '成员邮箱',
            '权限级别',
            '权限级别值',
            '成员状态',
            '成员加入时间',
            '成员过期时间'
        ]
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
        writer.writeheader()

        # 遍历每个组获取详细信息
        for idx, group in enumerate(groups, 1):
            print(f"处理组中... ({idx}/{len(groups)}) {group.get('full_path', '未知组')}")

            # 获取组的所有成员
            members = get_group_members(group['id'])

            if members:
                # 为每个成员创建一行记录
                for member in members:
                    row = {
                        '组ID': group.get('id', ''),
                        '组名称': group.get('name', ''),
                        '组路径': group.get('full_path', ''),
                        '组描述': group.get('description', ''),
                        '组创建时间': group.get('created_at', ''),
                        '组项目数': group.get('projects_count', 0),
                        '组子组数': group.get('subgroups_count', 0),
                        '组可见性': group.get('visibility', ''),
                        '成员姓名': member.get('name', ''),
                        '成员用户名': member.get('username', ''),
                        '成员邮箱': member.get('email', ''),
                        '权限级别': member.get('access_level', ''),
                        '权限级别值': member.get('access_level_value', ''),
                        '成员状态': member.get('state', ''),
                        '成员加入时间': member.get('created_at', ''),
                        '成员过期时间': member.get('expires_at', '')
                    }
                    writer.writerow(row)
            else:
                # 如果组没有成员，仍然记录组信息
                row = {
                    '组ID': group.get('id', ''),
                    '组名称': group.get('name', ''),
                    '组路径': group.get('full_path', ''),
                    '组描述': group.get('description', ''),
                    '组创建时间': group.get('created_at', ''),
                    '组项目数': group.get('projects_count', 0),
                    '组子组数': group.get('subgroups_count', 0),
                    '组可见性': group.get('visibility', ''),
                    '成员姓名': '无成员',
                    '成员用户名': '',
                    '成员邮箱': '',
                    '权限级别': '',
                    '权限级别值': '',
                    '成员状态': '',
                    '成员加入时间': '',
                    '成员过期时间': ''
                }
                writer.writerow(row)

    print(f"完成! 结果已保存到 {csv_filename}")

if __name__ == "__main__":
    main()
