#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
import requests
import time
import logging
import hmac
import hashlib
import base64
import urllib.parse
import asyncio
import aiohttp
import ssl
import os
from typing import List, Tuple, Dict, Optional

'''
## nohup python /root/ajie/domain_monitor_async.py &
钉钉机器人告警 - 异步优化版本
每次限制10个并发请求
'''

# 配置参数
MAX_CONCURRENT = 10  # 最大并发请求数
REQUEST_TIMEOUT = 5  # 请求超时时间（秒）
ALERT_INTERVAL = 600  # 告警间隔（秒）
RETRY_COUNT = 3  # 重试次数
RETRY_DELAY = 10  # 重试间隔（秒）
VERIFY_SSL = True  # SSL证书验证开关

# 常见CA证书路径，按优先级排序
CA_CERT_PATHS = [
    '/etc/ssl/certs/ca-certificates.crt',  # Debian/Ubuntu/Gentoo
    '/etc/pki/tls/certs/ca-bundle.crt',    # Fedora/RHEL/CentOS
    '/etc/ssl/ca-bundle.pem',              # OpenSUSE
    '/etc/pki/tls/cacert.pem',             # OpenELEC
    '/etc/ssl/cert.pem',                   # Alpine Linux
    '/etc/ssl/certs',                      # CA证书目录
]

# 自动检测可用的CA证书路径
CA_CERTS_PATH = None
for path in CA_CERT_PATHS:
    if os.path.exists(path):
        CA_CERTS_PATH = path
        break

# 如果找不到CA证书路径，使用requests的CA证书路径
if CA_CERTS_PATH is None:
    import certifi
    CA_CERTS_PATH = certifi.where()

domains = [
    'https://x61.jieruitech.info/123.txt',
    'http://x62.jieruitech.info/123.txt',
    'http://x63.jieruitech.info/123.txt',
    'http://x64.jieruitech.info/123.txt',
    'http://x65.jieruitech.info/123.txt',
    'http://x66.jieruitech.info/123.txt',

    'https://x111.jieruitech.info/123.txt',
    'http://x112.jieruitech.info/123.txt',
    'http://x113.jieruitech.info/123.txt',
    'http://x114.jieruitech.info/123.txt',
    'http://x115.jieruitech.info/123.txt',
    'http://x116.jieruitech.info/123.txt',

    'http://103.211.125.242/123.txt',  ##x43
    'http://103.211.125.243/123.txt',
    'http://103.211.125.244/123.txt',
    'http://103.211.125.245/123.txt',
    'http://103.211.125.246/123.txt',
    'https://x43001.jieruitech.info/123.txt',
    'http://107.163.142.131/123.txt',
    'http://107.163.142.132/123.txt',
    'http://107.163.142.133/123.txt',
    'http://107.163.142.134/123.txt',
    'http://107.163.142.135/123.txt',
    'http://107.163.142.136/123.txt',
    'http://107.163.142.137/123.txt',
    'http://107.163.142.138/123.txt',
    'http://107.163.142.139/123.txt',
    'http://107.163.142.140/123.txt',
    'http://107.163.142.141/123.txt',
    'http://107.163.142.142/123.txt',
    'http://107.163.142.143/123.txt',
    'http://107.163.142.144/123.txt',
    'http://107.163.142.145/123.txt',
    'http://107.163.142.146/123.txt',
    'http://107.163.142.147/123.txt',
    'http://107.163.142.148/123.txt',
    'http://107.163.142.149/123.txt',
    'http://107.163.142.150/123.txt',
    'http://107.163.142.151/123.txt',
    'http://107.163.142.152/123.txt',
    'http://107.163.142.153/123.txt',
    'http://107.163.142.154/123.txt',
    'http://107.163.142.155/123.txt',
    'http://107.163.142.156/123.txt',
    'http://107.163.142.157/123.txt',
    'http://107.163.142.158/123.txt',
    'http://107.163.142.159/123.txt',
    'http://107.163.142.160/123.txt',
    'http://107.163.142.161/123.txt',
    'http://107.163.142.162/123.txt',
    'http://107.163.142.163/123.txt',
    'http://107.163.142.164/123.txt',
    'http://107.163.142.165/123.txt',
    'http://107.163.142.166/123.txt',
    'http://107.163.142.167/123.txt',
    'http://107.163.142.168/123.txt',
    'http://107.163.142.169/123.txt',
    'http://107.163.142.170/123.txt',
    'http://107.163.142.171/123.txt',
    'http://107.163.142.172/123.txt',
    'http://107.163.142.173/123.txt',
    'http://107.163.142.174/123.txt',
    'http://107.163.142.175/123.txt',
    'http://107.163.142.176/123.txt',
    'http://107.163.142.177/123.txt',
    'http://107.163.142.178/123.txt',
    'http://107.163.142.179/123.txt',
    'http://107.163.142.180/123.txt',
    'http://107.163.142.181/123.txt',
    'http://107.163.142.182/123.txt',
    'http://107.163.142.183/123.txt',
    'http://107.163.142.184/123.txt',
    'http://107.163.142.185/123.txt',
    'http://107.163.142.186/123.txt',
    'http://107.163.142.187/123.txt',
    'http://107.163.142.188/123.txt',
    'http://107.163.142.189/123.txt',
    'http://107.163.142.190/123.txt',
    'http://107.163.137.130/123.txt',
    'http://107.163.137.131/123.txt',
    'http://107.163.137.132/123.txt',
    'http://107.163.137.133/123.txt',
    'http://107.163.137.134/123.txt',
    'http://107.163.137.135/123.txt',
    'http://107.163.137.136/123.txt',
    'http://107.163.137.137/123.txt',
    'http://107.163.137.138/123.txt',
    'http://107.163.137.139/123.txt',
    'http://107.163.137.140/123.txt',
    'http://107.163.137.141/123.txt',
    'http://107.163.137.142/123.txt',
    'http://107.163.137.143/123.txt',
    'http://107.163.137.144/123.txt',
    'http://107.163.137.145/123.txt',
    'http://107.163.137.146/123.txt',
    'http://107.163.137.147/123.txt',
    'http://107.163.137.148/123.txt',
    'http://107.163.137.149/123.txt',
    'http://107.163.137.150/123.txt',
    'http://107.163.137.151/123.txt',
    'http://107.163.137.152/123.txt',
    'http://107.163.137.153/123.txt',
    'http://107.163.137.154/123.txt',
    'http://107.163.137.155/123.txt',
    'http://107.163.137.156/123.txt',
    'http://107.163.137.157/123.txt',
    'http://107.163.137.158/123.txt',
    'http://107.163.137.159/123.txt',
    'http://107.163.137.160/123.txt',
    'http://107.163.137.161/123.txt',
    'http://107.163.137.162/123.txt',
    'http://107.163.137.163/123.txt',
    'http://107.163.137.164/123.txt',
    'http://107.163.137.165/123.txt',
    'http://107.163.137.166/123.txt',
    'http://107.163.137.167/123.txt',
    'http://107.163.137.168/123.txt',
    'http://107.163.137.169/123.txt',
    'http://107.163.137.170/123.txt',
    'http://107.163.137.171/123.txt',
    'http://107.163.137.172/123.txt',
    'http://107.163.137.173/123.txt',
    'http://107.163.137.174/123.txt',
    'http://107.163.137.175/123.txt',
    'http://107.163.137.176/123.txt',
    'http://107.163.137.177/123.txt',
    'http://107.163.137.178/123.txt',
    'http://107.163.137.179/123.txt',
    'http://107.163.137.180/123.txt',
    'http://107.163.137.181/123.txt',
    'http://107.163.137.182/123.txt',
    'http://107.163.137.183/123.txt',
    'http://107.163.137.184/123.txt',
    'http://107.163.137.185/123.txt',
    'http://107.163.137.186/123.txt',
    'http://107.163.137.187/123.txt',
    'http://107.163.137.188/123.txt',
    'http://107.163.137.189/123.txt',
    'http://107.163.137.190/123.txt',
    'http://23.231.169.130/123.txt',
    'http://23.231.169.131/123.txt',
    'http://23.231.169.132/123.txt',
    'http://23.231.169.133/123.txt',
    'http://23.231.169.134/123.txt',
    'http://23.231.169.135/123.txt',
    'http://23.231.169.136/123.txt',
    'http://23.231.169.137/123.txt',
    'http://23.231.169.138/123.txt',
    'http://23.231.169.139/123.txt',
    'http://23.231.169.140/123.txt',
    'http://23.231.169.141/123.txt',
    'http://23.231.169.142/123.txt',
    'http://23.231.169.143/123.txt',
    'http://23.231.169.144/123.txt',
    'http://23.231.169.145/123.txt',
    'http://23.231.169.146/123.txt',
    'http://23.231.169.147/123.txt',
    'http://23.231.169.148/123.txt',
    'http://23.231.169.149/123.txt',
    'http://23.231.169.150/123.txt',
    'http://23.231.169.151/123.txt',
    'http://23.231.169.152/123.txt',
    'http://23.231.169.153/123.txt',
    'http://23.231.169.154/123.txt',
    'http://23.231.169.155/123.txt',
    'http://23.231.169.156/123.txt',
    'http://23.231.169.157/123.txt',
    'http://23.231.169.158/123.txt',
    'http://23.231.169.159/123.txt',
    'http://23.231.169.160/123.txt',
    'http://23.231.169.161/123.txt',
    'http://23.231.169.162/123.txt',
    'http://23.231.169.163/123.txt',
    'http://23.231.169.164/123.txt',
    'http://23.231.169.165/123.txt',
    'http://23.231.169.166/123.txt',
    'http://23.231.169.167/123.txt',
    'http://23.231.169.168/123.txt',
    'http://23.231.169.169/123.txt',
    'http://23.231.169.170/123.txt',
    'http://23.231.169.171/123.txt',
    'http://23.231.169.172/123.txt',
    'http://23.231.169.173/123.txt',
    'http://23.231.169.174/123.txt',
    'http://23.231.169.175/123.txt',
    'http://23.231.169.176/123.txt',
    'http://23.231.169.177/123.txt',
    'http://23.231.169.178/123.txt',
    'http://23.231.169.179/123.txt',
    'http://23.231.169.180/123.txt',
    'http://23.231.169.181/123.txt',
    'http://23.231.169.182/123.txt',
    'http://23.231.169.183/123.txt',
    'http://23.231.169.184/123.txt',
    'http://23.231.169.185/123.txt',
    'http://23.231.169.186/123.txt',
    'http://23.231.169.187/123.txt',
    'http://23.231.169.188/123.txt',
    'http://23.231.169.189/123.txt',
    'http://23.231.169.190/123.txt',
    'http://23.231.172.130/123.txt',
    'http://23.231.172.131/123.txt',
    'http://23.231.172.132/123.txt',
    'http://23.231.172.133/123.txt',
    'http://23.231.172.134/123.txt',
    'http://23.231.172.135/123.txt',
    'http://23.231.172.136/123.txt',
    'http://23.231.172.137/123.txt',
    'http://23.231.172.138/123.txt',
    'http://23.231.172.139/123.txt',
    'http://23.231.172.140/123.txt',
    'http://23.231.172.141/123.txt',
    'http://23.231.172.142/123.txt',
    'http://23.231.172.143/123.txt',
    'http://23.231.172.144/123.txt',
    'http://23.231.172.145/123.txt',
    'http://23.231.172.146/123.txt',
    'http://23.231.172.147/123.txt',
    'http://23.231.172.148/123.txt',
    'http://23.231.172.149/123.txt',
    'http://23.231.172.150/123.txt',
    'http://23.231.172.151/123.txt',
    'http://23.231.172.152/123.txt',
    'http://23.231.172.153/123.txt',
    'http://23.231.172.154/123.txt',
    'http://23.231.172.155/123.txt',
    'http://23.231.172.156/123.txt',
    'http://23.231.172.157/123.txt',
    'http://23.231.172.158/123.txt',
    'http://23.231.172.159/123.txt',
    'http://23.231.172.160/123.txt',
    'http://23.231.172.161/123.txt',
    'http://23.231.172.162/123.txt',
    'http://23.231.172.163/123.txt',
    'http://23.231.172.164/123.txt',
    'http://23.231.172.165/123.txt',
    'http://23.231.172.166/123.txt',
    'http://23.231.172.167/123.txt',
    'http://23.231.172.168/123.txt',
    'http://23.231.172.169/123.txt',
    'http://23.231.172.170/123.txt',
    'http://23.231.172.171/123.txt',
    'http://23.231.172.172/123.txt',
    'http://23.231.172.173/123.txt',
    'http://23.231.172.174/123.txt',
    'http://23.231.172.175/123.txt',
    'http://23.231.172.176/123.txt',
    'http://23.231.172.177/123.txt',
    'http://23.231.172.178/123.txt',
    'http://23.231.172.179/123.txt',
    'http://23.231.172.180/123.txt',
    'http://23.231.172.181/123.txt',
    'http://23.231.172.182/123.txt',
    'http://23.231.172.183/123.txt',
    'http://23.231.172.184/123.txt',
    'http://23.231.172.185/123.txt',
    'http://23.231.172.186/123.txt',
    'http://23.231.172.187/123.txt',
    'http://23.231.172.188/123.txt',
    'http://23.231.172.189/123.txt',
    'http://23.231.172.190/123.txt',

    'http://107.163.128.2/123.txt',  ##x46
    'http://107.163.128.3/123.txt',
    'http://107.163.128.4/123.txt',
    'http://107.163.128.5/123.txt',
    'http://107.163.128.6/123.txt',
    'http://107.163.128.7/123.txt',
    'http://107.163.128.8/123.txt',
    'http://107.163.128.9/123.txt',
    'http://107.163.128.10/123.txt',
    'http://107.163.128.11/123.txt',
    'http://107.163.128.12/123.txt',
    'http://107.163.128.13/123.txt',
    'http://107.163.128.14/123.txt',
    'http://107.163.128.15/123.txt',
    'http://107.163.128.16/123.txt',
    'http://107.163.128.17/123.txt',
    'http://107.163.128.18/123.txt',
    'http://107.163.128.19/123.txt',
    'http://107.163.128.20/123.txt',
    'http://107.163.128.21/123.txt',
    'http://107.163.128.22/123.txt',
    'http://107.163.128.23/123.txt',
    'http://107.163.128.24/123.txt',
    'http://107.163.128.25/123.txt',
    'http://107.163.128.26/123.txt',
    'http://107.163.128.27/123.txt',
    'http://107.163.128.28/123.txt',
    'http://107.163.128.29/123.txt',
    'http://107.163.128.30/123.txt',
    'http://107.163.128.31/123.txt',
    'http://107.163.128.32/123.txt',
    'http://107.163.128.33/123.txt',
    'http://107.163.128.34/123.txt',
    'http://107.163.128.35/123.txt',
    'http://107.163.128.36/123.txt',
    'http://107.163.128.37/123.txt',
    'http://107.163.128.38/123.txt',
    'http://107.163.128.39/123.txt',
    'http://107.163.128.40/123.txt',
    'http://107.163.128.41/123.txt',
    'http://107.163.128.42/123.txt',
    'http://107.163.128.43/123.txt',
    'http://107.163.128.44/123.txt',
    'http://107.163.128.45/123.txt',
    'http://107.163.128.46/123.txt',
    'http://107.163.128.47/123.txt',
    'http://107.163.128.48/123.txt',
    'http://107.163.128.49/123.txt',
    'http://107.163.128.50/123.txt',
    'http://107.163.128.51/123.txt',
    'http://107.163.128.52/123.txt',
    'http://107.163.128.53/123.txt',
    'http://107.163.128.54/123.txt',
    'http://107.163.128.55/123.txt',
    'http://107.163.128.56/123.txt',
    'http://107.163.128.57/123.txt',
    'http://107.163.128.58/123.txt',
    'http://107.163.128.59/123.txt',
    'http://107.163.128.60/123.txt',
    'http://107.163.128.61/123.txt',
    'http://107.163.128.62/123.txt',
    'http://107.163.128.63/123.txt',
    'http://107.163.128.64/123.txt',
    'http://107.163.128.65/123.txt',
    'http://107.163.128.66/123.txt',
    'http://107.163.128.67/123.txt',
    'http://107.163.128.68/123.txt',
    'http://107.163.128.69/123.txt',
    'http://107.163.128.70/123.txt',
    'http://107.163.128.71/123.txt',
    'http://107.163.128.72/123.txt',
    'http://107.163.128.73/123.txt',
    'http://107.163.128.74/123.txt',
    'http://107.163.128.75/123.txt',
    'http://107.163.128.76/123.txt',
    'http://107.163.128.77/123.txt',
    'http://107.163.128.78/123.txt',
    'http://107.163.128.79/123.txt',
    'http://107.163.128.80/123.txt',
    'http://107.163.128.81/123.txt',
    'http://107.163.128.82/123.txt',
    'http://107.163.128.83/123.txt',
    'http://107.163.128.84/123.txt',
    'http://107.163.128.85/123.txt',
    'http://107.163.128.86/123.txt',
    'http://107.163.128.87/123.txt',
    'http://107.163.128.88/123.txt',
    'http://107.163.128.89/123.txt',
    'http://107.163.128.90/123.txt',
    'http://107.163.128.91/123.txt',
    'http://107.163.128.92/123.txt',
    'http://107.163.128.93/123.txt',
    'http://107.163.128.94/123.txt',
    'http://107.163.128.95/123.txt',
    'http://107.163.128.96/123.txt',
    'http://107.163.128.97/123.txt',
    'http://107.163.128.98/123.txt',
    'http://107.163.128.99/123.txt',
    'http://107.163.128.100/123.txt',
    'http://107.163.128.101/123.txt',
    'http://107.163.128.102/123.txt',
    'http://107.163.128.103/123.txt',
    'http://107.163.128.104/123.txt',
    'http://107.163.128.105/123.txt',
    'http://107.163.128.106/123.txt',
    'http://107.163.128.107/123.txt',
    'http://107.163.128.108/123.txt',
    'http://107.163.128.109/123.txt',
    'http://107.163.128.110/123.txt',
    'http://107.163.128.111/123.txt',
    'http://107.163.128.112/123.txt',
    'http://107.163.128.113/123.txt',
    'http://107.163.128.114/123.txt',
    'http://107.163.128.115/123.txt',
    'http://107.163.128.116/123.txt',
    'http://107.163.128.117/123.txt',
    'http://107.163.128.118/123.txt',
    'http://107.163.128.119/123.txt',
    'http://107.163.128.120/123.txt',
    'http://107.163.128.121/123.txt',
    'http://107.163.128.122/123.txt',
    'http://107.163.128.123/123.txt',
    'http://107.163.128.124/123.txt',
    'http://107.163.128.125/123.txt',
    'http://107.163.128.126/123.txt',
    'http://107.163.128.127/123.txt',
    'http://107.163.128.128/123.txt',
    'http://107.163.128.129/123.txt',
    'http://107.163.128.130/123.txt',
    'http://107.163.128.131/123.txt',
    'http://107.163.128.132/123.txt',
    'http://107.163.128.133/123.txt',
    'http://107.163.128.134/123.txt',
    'http://107.163.128.135/123.txt',
    'http://107.163.128.136/123.txt',
    'http://107.163.128.137/123.txt',
    'http://107.163.128.138/123.txt',
    'http://107.163.128.139/123.txt',
    'http://107.163.128.140/123.txt',
    'http://107.163.128.141/123.txt',
    'http://107.163.128.142/123.txt',
    'http://107.163.128.143/123.txt',
    'http://107.163.128.144/123.txt',
    'http://107.163.128.145/123.txt',
    'http://107.163.128.146/123.txt',
    'http://107.163.128.147/123.txt',
    'http://107.163.128.148/123.txt',
    'http://107.163.128.149/123.txt',
    'http://107.163.128.150/123.txt',
    'http://107.163.128.151/123.txt',
    'http://107.163.128.152/123.txt',
    'http://107.163.128.153/123.txt',
    'http://107.163.128.154/123.txt',
    'http://107.163.128.155/123.txt',
    'http://107.163.128.156/123.txt',
    'http://107.163.128.157/123.txt',
    'http://107.163.128.158/123.txt',
    'http://107.163.128.159/123.txt',
    'http://107.163.128.160/123.txt',
    'http://107.163.128.161/123.txt',
    'http://107.163.128.162/123.txt',
    'http://107.163.128.163/123.txt',
    'http://107.163.128.164/123.txt',
    'http://107.163.128.165/123.txt',
    'http://107.163.128.166/123.txt',
    'http://107.163.128.167/123.txt',
    'http://107.163.128.168/123.txt',
    'http://107.163.128.169/123.txt',
    'http://107.163.128.170/123.txt',
    'http://107.163.128.171/123.txt',
    'http://107.163.128.172/123.txt',
    'http://107.163.128.173/123.txt',
    'http://107.163.128.174/123.txt',
    'http://107.163.128.175/123.txt',
    'http://107.163.128.176/123.txt',
    'http://107.163.128.177/123.txt',
    'http://107.163.128.178/123.txt',
    'http://107.163.128.179/123.txt',
    'http://107.163.128.180/123.txt',
    'http://107.163.128.181/123.txt',
    'http://107.163.128.182/123.txt',
    'http://107.163.128.183/123.txt',
    'http://107.163.128.184/123.txt',
    'http://107.163.128.185/123.txt',
    'http://107.163.128.186/123.txt',
    'http://107.163.128.187/123.txt',
    'http://107.163.128.188/123.txt',
    'http://107.163.128.189/123.txt',
    'http://107.163.128.190/123.txt',
    'http://107.163.128.191/123.txt',
    'http://107.163.128.192/123.txt',
    'http://107.163.128.193/123.txt',
    'http://107.163.128.194/123.txt',
    'http://107.163.128.195/123.txt',
    'http://107.163.128.196/123.txt',
    'http://107.163.128.197/123.txt',
    'http://107.163.128.198/123.txt',
    'http://107.163.128.199/123.txt',
    'http://107.163.128.200/123.txt',
    'http://107.163.128.201/123.txt',
    'http://107.163.128.202/123.txt',
    'http://107.163.128.203/123.txt',
    'http://107.163.128.204/123.txt',
    'http://107.163.128.205/123.txt',
    'http://107.163.128.206/123.txt',
    'http://107.163.128.207/123.txt',
    'http://107.163.128.208/123.txt',
    'http://107.163.128.209/123.txt',
    'http://107.163.128.210/123.txt',
    'http://107.163.128.211/123.txt',
    'http://107.163.128.212/123.txt',
    'http://107.163.128.213/123.txt',
    'http://107.163.128.214/123.txt',
    'http://107.163.128.215/123.txt',
    'http://107.163.128.216/123.txt',
    'http://107.163.128.217/123.txt',
    'http://107.163.128.218/123.txt',
    'http://107.163.128.219/123.txt',
    'http://107.163.128.220/123.txt',
    'http://107.163.128.221/123.txt',
    'http://107.163.128.222/123.txt',
    'http://107.163.128.223/123.txt',
    'http://107.163.128.224/123.txt',
    'http://107.163.128.225/123.txt',
    'http://107.163.128.226/123.txt',
    'http://107.163.128.227/123.txt',
    'http://107.163.128.228/123.txt',
    'http://107.163.128.229/123.txt',
    'http://107.163.128.230/123.txt',
    'http://107.163.128.231/123.txt',
    'http://107.163.128.232/123.txt',
    'http://107.163.128.233/123.txt',
    'http://107.163.128.234/123.txt',
    'http://107.163.128.235/123.txt',
    'http://107.163.128.236/123.txt',
    'http://107.163.128.237/123.txt',
    'http://107.163.128.238/123.txt',
    'http://107.163.128.239/123.txt',
    'http://107.163.128.240/123.txt',
    'http://107.163.128.241/123.txt',
    'http://107.163.128.242/123.txt',
    'http://107.163.128.243/123.txt',
    'http://107.163.128.244/123.txt',
    'http://107.163.128.245/123.txt',
    'http://107.163.128.246/123.txt',
    'http://107.163.128.247/123.txt',
    'http://107.163.128.248/123.txt',
    'http://107.163.128.249/123.txt',
    'http://107.163.128.250/123.txt',
    'http://107.163.128.251/123.txt',
    'http://107.163.128.252/123.txt',
    'http://107.163.128.253/123.txt',
    'http://107.163.128.254/123.txt',

    'http://103.57.231.106/123.txt',  ##x47
    'http://103.57.231.107/123.txt',
    'http://103.57.231.108/123.txt',
    'http://103.57.231.109/123.txt',
    'http://103.57.231.110/123.txt',
    'http://23.231.139.2/123.txt',
    'http://23.231.139.3/123.txt',
    'http://23.231.139.4/123.txt',
    'http://23.231.139.5/123.txt',
    'http://23.231.139.6/123.txt',
    'http://23.231.139.7/123.txt',
    'http://23.231.139.8/123.txt',
    'http://23.231.139.9/123.txt',
    'http://23.231.139.10/123.txt',
    'http://23.231.139.11/123.txt',
    'http://23.231.139.12/123.txt',
    'http://23.231.139.13/123.txt',
    'http://23.231.139.14/123.txt',
    'http://23.231.139.15/123.txt',
    'http://23.231.139.16/123.txt',
    'http://23.231.139.17/123.txt',
    'http://23.231.139.18/123.txt',
    'http://23.231.139.19/123.txt',
    'http://23.231.139.20/123.txt',
    'http://23.231.139.21/123.txt',
    'http://23.231.139.22/123.txt',
    'http://23.231.139.23/123.txt',
    'http://23.231.139.24/123.txt',
    'http://23.231.139.25/123.txt',
    'http://23.231.139.26/123.txt',
    'http://23.231.139.27/123.txt',
    'http://23.231.139.28/123.txt',
    'http://23.231.139.29/123.txt',
    'http://23.231.139.30/123.txt',
    'http://23.231.139.31/123.txt',
    'http://23.231.139.32/123.txt',
    'http://23.231.139.33/123.txt',
    'http://23.231.139.34/123.txt',
    'http://23.231.139.35/123.txt',
    'http://23.231.139.36/123.txt',
    'http://23.231.139.37/123.txt',
    'http://23.231.139.38/123.txt',
    'http://23.231.139.39/123.txt',
    'http://23.231.139.40/123.txt',
    'http://23.231.139.41/123.txt',
    'http://23.231.139.42/123.txt',
    'http://23.231.139.43/123.txt',
    'http://23.231.139.44/123.txt',
    'http://23.231.139.45/123.txt',
    'http://23.231.139.46/123.txt',
    'http://23.231.139.47/123.txt',
    'http://23.231.139.48/123.txt',
    'http://23.231.139.49/123.txt',
    'http://23.231.139.50/123.txt',
    'http://23.231.139.51/123.txt',
    'http://23.231.139.52/123.txt',
    'http://23.231.139.53/123.txt',
    'http://23.231.139.54/123.txt',
    'http://23.231.139.55/123.txt',
    'http://23.231.139.56/123.txt',
    'http://23.231.139.57/123.txt',
    'http://23.231.139.58/123.txt',
    'http://23.231.139.59/123.txt',
    'http://23.231.139.60/123.txt',
    'http://23.231.139.61/123.txt',
    'http://23.231.139.62/123.txt',
    'http://23.231.139.63/123.txt',
    'http://23.231.139.64/123.txt',
    'http://23.231.139.65/123.txt',
    'http://23.231.139.66/123.txt',
    'http://23.231.139.67/123.txt',
    'http://23.231.139.68/123.txt',
    'http://23.231.139.69/123.txt',
    'http://23.231.139.70/123.txt',
    'http://23.231.139.71/123.txt',
    'http://23.231.139.72/123.txt',
    'http://23.231.139.73/123.txt',
    'http://23.231.139.74/123.txt',
    'http://23.231.139.75/123.txt',
    'http://23.231.139.76/123.txt',
    'http://23.231.139.77/123.txt',
    'http://23.231.139.78/123.txt',
    'http://23.231.139.79/123.txt',
    'http://23.231.139.80/123.txt',
    'http://23.231.139.81/123.txt',
    'http://23.231.139.82/123.txt',
    'http://23.231.139.83/123.txt',
    'http://23.231.139.84/123.txt',
    'http://23.231.139.85/123.txt',
    'http://23.231.139.86/123.txt',
    'http://23.231.139.87/123.txt',
    'http://23.231.139.88/123.txt',
    'http://23.231.139.89/123.txt',
    'http://23.231.139.90/123.txt',
    'http://23.231.139.91/123.txt',
    'http://23.231.139.92/123.txt',
    'http://23.231.139.93/123.txt',
    'http://23.231.139.94/123.txt',
    'http://23.231.139.95/123.txt',
    'http://23.231.139.96/123.txt',
    'http://23.231.139.97/123.txt',
    'http://23.231.139.98/123.txt',
    'http://23.231.139.99/123.txt',
    'http://23.231.139.100/123.txt',
    'http://23.231.139.101/123.txt',
    'http://23.231.139.102/123.txt',
    'http://23.231.139.103/123.txt',
    'http://23.231.139.104/123.txt',
    'http://23.231.139.105/123.txt',
    'http://23.231.139.106/123.txt',
    'http://23.231.139.107/123.txt',
    'http://23.231.139.108/123.txt',
    'http://23.231.139.109/123.txt',
    'http://23.231.139.110/123.txt',
    'http://23.231.139.111/123.txt',
    'http://23.231.139.112/123.txt',
    'http://23.231.139.113/123.txt',
    'http://23.231.139.114/123.txt',
    'http://23.231.139.115/123.txt',
    'http://23.231.139.116/123.txt',
    'http://23.231.139.117/123.txt',
    'http://23.231.139.118/123.txt',
    'http://23.231.139.119/123.txt',
    'http://23.231.139.120/123.txt',
    'http://23.231.139.121/123.txt',
    'http://23.231.139.122/123.txt',
    'http://23.231.139.123/123.txt',
    'http://23.231.139.124/123.txt',
    'http://23.231.139.125/123.txt',
    'http://23.231.139.126/123.txt',
    'http://23.231.139.127/123.txt',
    'http://23.231.139.128/123.txt',
    'http://23.231.139.129/123.txt',
    'http://23.231.139.130/123.txt',
    'http://23.231.139.131/123.txt',
    'http://23.231.139.132/123.txt',
    'http://23.231.139.133/123.txt',
    'http://23.231.139.134/123.txt',
    'http://23.231.139.135/123.txt',
    'http://23.231.139.136/123.txt',
    'http://23.231.139.137/123.txt',
    'http://23.231.139.138/123.txt',
    'http://23.231.139.139/123.txt',
    'http://23.231.139.140/123.txt',
    'http://23.231.139.141/123.txt',
    'http://23.231.139.142/123.txt',
    'http://23.231.139.143/123.txt',
    'http://23.231.139.144/123.txt',
    'http://23.231.139.145/123.txt',
    'http://23.231.139.146/123.txt',
    'http://23.231.139.147/123.txt',
    'http://23.231.139.148/123.txt',
    'http://23.231.139.149/123.txt',
    'http://23.231.139.150/123.txt',
    'http://23.231.139.151/123.txt',
    'http://23.231.139.152/123.txt',
    'http://23.231.139.153/123.txt',
    'http://23.231.139.154/123.txt',
    'http://23.231.139.155/123.txt',
    'http://23.231.139.156/123.txt',
    'http://23.231.139.157/123.txt',
    'http://23.231.139.158/123.txt',
    'http://23.231.139.159/123.txt',
    'http://23.231.139.160/123.txt',
    'http://23.231.139.161/123.txt',
    'http://23.231.139.162/123.txt',
    'http://23.231.139.163/123.txt',
    'http://23.231.139.164/123.txt',
    'http://23.231.139.165/123.txt',
    'http://23.231.139.166/123.txt',
    'http://23.231.139.167/123.txt',
    'http://23.231.139.168/123.txt',
    'http://23.231.139.169/123.txt',
    'http://23.231.139.170/123.txt',
    'http://23.231.139.171/123.txt',
    'http://23.231.139.172/123.txt',
    'http://23.231.139.173/123.txt',
    'http://23.231.139.174/123.txt',
    'http://23.231.139.175/123.txt',
    'http://23.231.139.176/123.txt',
    'http://23.231.139.177/123.txt',
    'http://23.231.139.178/123.txt',
    'http://23.231.139.179/123.txt',
    'http://23.231.139.180/123.txt',
    'http://23.231.139.181/123.txt',
    'http://23.231.139.182/123.txt',
    'http://23.231.139.183/123.txt',
    'http://23.231.139.184/123.txt',
    'http://23.231.139.185/123.txt',
    'http://23.231.139.186/123.txt',
    'http://23.231.139.187/123.txt',
    'http://23.231.139.188/123.txt',
    'http://23.231.139.189/123.txt',
    # 'http://23.231.139.190/123.txt',
    'http://23.231.139.191/123.txt',
    'http://23.231.139.192/123.txt',
    'http://23.231.139.193/123.txt',
    'http://23.231.139.194/123.txt',
    'http://23.231.139.195/123.txt',
    'http://23.231.139.196/123.txt',
    'http://23.231.139.197/123.txt',
    'http://23.231.139.198/123.txt',
    'http://23.231.139.199/123.txt',
    'http://23.231.139.200/123.txt',
    'http://23.231.139.201/123.txt',
    'http://23.231.139.202/123.txt',
    'http://23.231.139.203/123.txt',
    'http://23.231.139.204/123.txt',
    'http://23.231.139.205/123.txt',
    'http://23.231.139.206/123.txt',
    'http://23.231.139.207/123.txt',
    'http://23.231.139.208/123.txt',
    'http://23.231.139.209/123.txt',
    'http://23.231.139.210/123.txt',
    'http://23.231.139.211/123.txt',
    'http://23.231.139.212/123.txt',
    'http://23.231.139.213/123.txt',
    'http://23.231.139.214/123.txt',
    'http://23.231.139.215/123.txt',
    'http://23.231.139.216/123.txt',
    'http://23.231.139.217/123.txt',
    'http://23.231.139.218/123.txt',
    'http://23.231.139.219/123.txt',
    'http://23.231.139.220/123.txt',
    'http://23.231.139.221/123.txt',
    'http://23.231.139.222/123.txt',
    'http://23.231.139.223/123.txt',
    'http://23.231.139.224/123.txt',
    'http://23.231.139.225/123.txt',
    'http://23.231.139.226/123.txt',
    'http://23.231.139.227/123.txt',
    'http://23.231.139.228/123.txt',
    'http://23.231.139.229/123.txt',
    'http://23.231.139.230/123.txt',
    'http://23.231.139.231/123.txt',
    'http://23.231.139.232/123.txt',
    'http://23.231.139.233/123.txt',
    'http://23.231.139.234/123.txt',
    'http://23.231.139.235/123.txt',
    'http://23.231.139.236/123.txt',
    'http://23.231.139.237/123.txt',
    'http://23.231.139.238/123.txt',
    'http://23.231.139.239/123.txt',
    'http://23.231.139.240/123.txt',
    'http://23.231.139.241/123.txt',
    'http://23.231.139.242/123.txt',
    'http://23.231.139.243/123.txt',
    'http://23.231.139.244/123.txt',
    'http://23.231.139.245/123.txt',
    'http://23.231.139.246/123.txt',
    'http://23.231.139.247/123.txt',
    'http://23.231.139.248/123.txt',
    'http://23.231.139.249/123.txt',
    'http://23.231.139.250/123.txt',
    'http://23.231.139.251/123.txt',
    'http://23.231.139.252/123.txt',
    'http://23.231.139.253/123.txt',
    'http://23.231.139.254/123.txt',

    'http://192.197.113.60/123.txt',  ##x12
    'http://192.197.113.61/123.txt',
    'http://192.197.113.66/123.txt',
    'http://192.197.113.68/123.txt',
    'http://192.197.113.80/123.txt',
    'http://192.197.113.84/123.txt',

    'http://h61.jieruitech.info/123.txt',
    'http://h62.jieruitech.info/123.txt',
    'http://h63.jieruitech.info/123.txt',
    'http://h64.jieruitech.info/123.txt',
    'http://h65.jieruitech.info/123.txt',
    'http://h66.jieruitech.info/123.txt',
    'http://h67.jieruitech.info/123.txt',

    'http://m31.jieruitech.info/123.txt',
    'http://m32.jieruitech.info/123.txt',
    'http://m33.jieruitech.info/123.txt',
    'http://m34.jieruitech.info/123.txt',
    'http://m35.jieruitech.info/123.txt',
    'http://m36.jieruitech.info/123.txt',
    'http://m37.jieruitech.info/123.txt',
    'http://m38.jieruitech.info/123.txt',
    'http://m39.jieruitech.info/123.txt',
    'http://m310.jieruitech.info/123.txt',
   'http://m311.jieruitech.info/123.txt',
   'http://m312.jieruitech.info/123.txt',
   'http://m313.jieruitech.info/123.txt',
   'http://m314.jieruitech.info/123.txt',
   'http://m315.jieruitech.info/123.txt',
   'http://m316.jieruitech.info/123.txt',
   'http://m317.jieruitech.info/123.txt',
   'http://m318.jieruitech.info/123.txt',
   'http://m319.jieruitech.info/123.txt',
   'http://m320.jieruitech.info/123.txt',
   'http://m321.jieruitech.info/123.txt',
   'http://m322.jieruitech.info/123.txt',
   'http://m323.jieruitech.info/123.txt',
   'http://m324.jieruitech.info/123.txt',
   'http://m325.jieruitech.info/123.txt',
   'http://m326.jieruitech.info/123.txt',
   'http://m327.jieruitech.info/123.txt',
   'http://m328.jieruitech.info/123.txt',
   'http://m329.jieruitech.info/123.txt',
   'http://m330.jieruitech.info/123.txt',
   'http://m331.jieruitech.info/123.txt',
   'http://m332.jieruitech.info/123.txt',
   'http://m333.jieruitech.info/123.txt',
   'http://m334.jieruitech.info/123.txt',
   'http://m335.jieruitech.info/123.txt',
   'http://m336.jieruitech.info/123.txt',
   'http://m337.jieruitech.info/123.txt',
   'http://m338.jieruitech.info/123.txt',
   'http://m339.jieruitech.info/123.txt',
   'http://m340.jieruitech.info/123.txt',
   'http://m341.jieruitech.info/123.txt',
   'http://m342.jieruitech.info/123.txt',
   'http://m343.jieruitech.info/123.txt',
   'http://m344.jieruitech.info/123.txt',
   'http://m345.jieruitech.info/123.txt',
   'http://m346.jieruitech.info/123.txt',
   'http://m347.jieruitech.info/123.txt',
   'http://m348.jieruitech.info/123.txt',
   'http://m349.jieruitech.info/123.txt',
   'http://m350.jieruitech.info/123.txt',

    'http://admin.buyusdt.me/123.txt',
    # 'http://ali.buyusdt.me/123.txt',
]

# 配置日志输出到文件
logging.basicConfig(filename='monitor.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def get_digest():  # 钉钉告警拼接
    # 取毫秒级别时间戳，round(x, n) 取x小数点后n位的结果，默认取整
    timestamp = str(round(time.time() * 1000))
    secret = 'SECcadb06773f5af50923dd2f63f5f61aef2aa3cf98da5f2fd03cf834297869ec6c'
    secret_enc = secret.encode('utf-8')  # utf-8编码
    string_to_sign = '{}\n{}'.format(timestamp, secret)  # 字符串格式化拼接
    string_to_sign_enc = string_to_sign.encode('utf-8')  # utf-8编码
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()  # HmacSHA256算法计算签名
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))  # Base64编码后进行urlEncode
    #  返回时间戳和计算好的编码拼接字符串，后面直接拼接到Webhook即可
    return f"&timestamp={timestamp}&sign={sign}"


# 钉钉告警
def warning_bot(message):
    data = {
        "msgtype": "text",
        "text": {
            "content": message
        },
    }
    # 机器人链接地址，发post请求 向钉钉机器人传递指令
    webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token' \
                  '=9fb7741f17e86008209eee7a3db93dd32caa9b26369fa6633a10c3d25de2cb8f '
    # 利用requests发送post请求
    req = requests.post(webhook_url + get_digest(), json=data)
    print(req.status_code)


def update_service_status_background(service: str, status: int):
    """
    后台更新服务状态到管理后台

    特点：
    - 在后台线程中执行，完全不阻塞主监控流程
    - 只在域名状态发生变化时调用（故障→正常 或 正常→故障）
    - 避免了原来每个正常域名都调用的性能问题

    参数：
    - service: 服务名称（从域名中提取）
    - status: 状态码（1=正常，2=异常）
    """
    import threading

    def _update():
        try:
            url = f"http://admin.buyusdt.me/api_service.html?password=nixcha20ihd&act=service_status&service={service}&status={status}"
            requests.get(url, timeout=8)
            logging.debug(f"服务状态更新成功: {service} -> {status}")
        except Exception as e:
            logging.warning(f"后台更新服务状态时发生异常：{e}")

    # 在后台线程中执行，不阻塞主流程
    thread = threading.Thread(target=_update, daemon=True)
    thread.start()


def extract_service_name(domain: str) -> str:
    """从域名中提取服务名称"""
    pattern = r'https?://([a-zA-Z0-9]+)\.'
    match = re.search(pattern, domain)
    return match.group(1) if match else "unknown"


async def check_domain_async(session: aiohttp.ClientSession, domain: str) -> Tuple[Optional[int], Optional[float]]:
    """异步检查域名状态"""
    try:
        start_time = time.time()
        async with session.get(domain, timeout=aiohttp.ClientTimeout(total=REQUEST_TIMEOUT)) as response:
            response_time = time.time() - start_time

            # 实时输出检查结果
            if response.status == 200:
                logging.info(f" {domain} 正常，响应码：{response.status}，响应时间：{response_time:.2f}s")
            else:
                logging.warning(f" {domain} 异常，响应码：{response.status}，响应时间：{response_time:.2f}s")

            return response.status, response_time
    except Exception as e:
        logging.warning(f" {domain} 请求失败: {e}")
        return None, None


def create_ssl_context():
    """创建SSL上下文，配置CA证书路径"""
    if not VERIFY_SSL:
        return False  # 如果禁用SSL验证，返回False
    
    # 创建默认SSL上下文
    ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
    
    # 如果找到CA证书路径，配置SSL上下文
    if CA_CERTS_PATH:
        if os.path.isdir(CA_CERTS_PATH):
            ssl_context.load_verify_locations(capath=CA_CERTS_PATH)
        else:
            ssl_context.load_verify_locations(cafile=CA_CERTS_PATH)
    
    return ssl_context


async def check_domains_batch(domains_batch: List[str]) -> Dict[str, Tuple[Optional[int], Optional[float]]]:
    """异步批量检查域名状态，限制并发数为10，实时显示结果"""
    results = {}
    total_count = len(domains_batch)

    logging.info(f"🚀 开始检查 {total_count} 个域名，最大并发数：{MAX_CONCURRENT}")

    # 创建SSL上下文
    ssl_context = create_ssl_context()

    # 创建连接器，限制连接数
    connector = aiohttp.TCPConnector(
        limit=MAX_CONCURRENT,
        limit_per_host=5,
        force_close=True,
        enable_cleanup_closed=True,
        ssl=ssl_context  # 使用自定义SSL上下文
    )

    # 创建会话
    timeout = aiohttp.ClientTimeout(total=REQUEST_TIMEOUT, connect=2)
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={'User-Agent': 'Domain-Monitor-Async/1.0'}
    ) as session:

        # 创建信号量限制并发数为10
        semaphore = asyncio.Semaphore(MAX_CONCURRENT)

        async def check_single_domain(domain: str):
            async with semaphore:
                return domain, await check_domain_async(session, domain)

        # 并发执行所有域名检查
        tasks = [check_single_domain(domain) for domain in domains_batch]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for result in batch_results:
            if isinstance(result, Exception):
                logging.error(f"批次检查时发生异常: {result}")
                continue
            domain, (status_code, response_time) = result
            results[domain] = (status_code, response_time)

    return results


def check_domain_sync(domain: str) -> Tuple[Optional[int], Optional[float]]:
    """同步检查域名状态 - 用于重试"""
    try:
        start_time = time.time()
        response = requests.get(domain, timeout=REQUEST_TIMEOUT, verify=VERIFY_SSL)
        response_time = time.time() - start_time
        return response.status_code, response_time
    except Exception:
        return None, None


async def retry_failed_domains(failed_domains: List[str], domain_status: Dict):
    """重试失败的域名"""
    for domain in failed_domains:
        service = extract_service_name(domain)
        current_time = int(time.time())
        success = False

        # 进行重试
        for attempt in range(RETRY_COUNT):
            try:
                response_code, response_time = check_domain_sync(domain)

                if response_code == 200:
                    success = True
                    logging.info(f"重试成功: {domain}，响应码：{response_code}，响应时间：{response_time:.2f}s")
                    # 重试成功，域名从未知状态变为正常，不需要更新服务状态
                    # 因为这个域名本来就不在故障状态，只是首次检查失败
                    break
                else:
                    logging.info(f"{domain} 第 {attempt + 1} 次重试失败")
                    if attempt < RETRY_COUNT - 1:
                        await asyncio.sleep(RETRY_DELAY)

            except Exception as e:
                logging.error(f"重试域名 {domain} 时发生异常: {e}")

        if not success:
            # 所有重试都失败，标记为故障 - 需要更新服务状态
            domain_status[domain]['is_down'] = True
            domain_status[domain]['last_alert'] = current_time
            domain_status[domain]['fail_count'] = RETRY_COUNT
            logging.warning(f"告警: {domain} is down!! (经过 {RETRY_COUNT} 次重试)")
            message = f"告警: {domain} is down!!"
            warning_bot(message)
            update_service_status_background(service, 2)  # 只在新故障时更新状态


async def monitor_domains_async():
    """异步域名监控主函数 - 每次限制10个并发请求"""
    # 初始化域名状态跟踪
    domain_status = {
        domain: {"last_alert": 0, "is_down": False, "fail_count": 0}
        for domain in domains
    }

    logging.info(f"开始异步域名监控，总域名数：{len(domains)}，最大并发：{MAX_CONCURRENT}")

    while True:
        cycle_start_time = time.time()
        failed_domains = []
        success_count = 0
        failure_count = 0

        logging.info(f"开始新一轮检查，总域名 {len(domains)} 个")

        try:
            # 异步批量检查所有域名（内部限制并发数为10）
            batch_results = await check_domains_batch(domains)

            # 处理检查结果
            for domain, (status_code, response_time) in batch_results.items():
                current_time = int(time.time())
                service = extract_service_name(domain)

                if domain_status[domain]['is_down']:
                    # 域名已经处于故障状态，检查是否恢复
                    if status_code == 200:
                        # 域名恢复了 - 需要更新服务状态
                        logging.warning(f"恢复: {domain} is up!! 响应时间: {response_time:.2f}s")
                        message = f"恢复: {domain} is up!!"
                        warning_bot(message)
                        domain_status[domain]['is_down'] = False
                        domain_status[domain]['last_alert'] = 0
                        domain_status[domain]['fail_count'] = 0
                        update_service_status_background(service, 1)  # 只在恢复时更新状态
                        success_count += 1
                    else:
                        # 域名仍然故障 - 不需要重复更新服务状态
                        domain_status[domain]['fail_count'] += 1
                        logging.info(f"{domain} 第 {domain_status[domain]['fail_count']} 次请求失败")
                        failure_count += 1

                        # 检查是否需要重复告警
                        elapsed_time = current_time - domain_status[domain]['last_alert']
                        if elapsed_time > ALERT_INTERVAL:
                            domain_status[domain]['last_alert'] = current_time
                            logging.warning(f"未恢复: {domain} is still down!!")
                            message = f"未恢复: {domain} is still down!!"
                            warning_bot(message)
                else:
                    # 域名正常状态，检查是否出现故障
                    if status_code == 200:
                        # 域名正常 - 不需要每次都更新服务状态
                        # 结果已在 check_domain_async 中实时显示，这里不重复记录
                        success_count += 1
                    else:
                        # 域名可能故障，加入失败列表等待重试
                        failed_domains.append(domain)
                        logging.warning(f" {domain} 首次检查失败，将进行重试")
                        failure_count += 1

            # 处理失败的域名 - 进行重试
            if failed_domains:
                logging.info(f"开始重试 {len(failed_domains)} 个失败域名")
                await retry_failed_domains(failed_domains, domain_status)

            # 计算本轮检查耗时
            cycle_time = time.time() - cycle_start_time
            logging.info(f"本轮检查完成，总耗时 {cycle_time:.2f}s，平均每个域名 {cycle_time/len(domains):.3f}s，成功 {success_count}，失败 {failure_count}")

            # 动态调整休息时间
            if cycle_time < 60:  # 如果一轮检查时间少于1分钟，适当休息
                rest_time = max(10, 60 - cycle_time)
                logging.info(f"本轮检查较快，休息 {rest_time:.1f}s")
                await asyncio.sleep(rest_time)

        except Exception as e:
            logging.error(f"监控过程中发生异常: {e}")
            await asyncio.sleep(30)  # 出错时休息30秒


if __name__ == '__main__':
    # 启动异步监控
    try:
        print("启动异步域名监控...")
        print(f"配置: 最大并发 {MAX_CONCURRENT}，请求超时 {REQUEST_TIMEOUT}s，告警间隔 {ALERT_INTERVAL}s")
        print(f"SSL验证: {'启用' if VERIFY_SSL else '禁用'}, CA证书路径: {CA_CERTS_PATH}")
        asyncio.run(monitor_domains_async())
    except KeyboardInterrupt:
        logging.info("监控程序被用户中断")
        print("监控已停止")
    except Exception as e:
        logging.error(f"监控程序发生异常: {e}")
        print(f"监控程序异常: {e}")
